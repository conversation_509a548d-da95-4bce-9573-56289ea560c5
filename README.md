# iDatas社工系统

专业的社工数据查询平台，提供网红猎魔、白底个户、身份证补齐等多种查询服务。快速、准确、安全的数据查询解决方案。

## 🌟 特性

- **极简设计**: 采用极简风+iOS风格设计，界面简洁美观
- **响应式布局**: 完美适配PC端和移动端设备
- **用户认证**: 安全的用户注册和登录系统
- **多种工具**: 提供基础信息查询、社交媒体查询等多种功能
- **VIP系统**: 支持普通用户和VIP用户权限管理
- **实时查询**: 集成上游API，提供实时数据查询服务

## 🚀 快速开始

### 环境要求

- PHP 8.0.2 或更高版本
- Nginx 1.15.11 或 Apache 2.4+
- 现代浏览器（Chrome、Firefox、Safari、Edge）

### 安装步骤

1. **下载项目文件**
   ```bash
   git clone https://github.com/your-repo/idatas.git
   cd idatas
   ```

2. **配置Web服务器**
   - 将项目文件放置在Web服务器根目录
   - 确保PHP扩展已启用：`json`, `curl`, `mbstring`
   - 配置URL重写（已包含.htaccess文件）

3. **设置权限**
   ```bash
   chmod 755 api/
   chmod 644 api/auth/users.json
   ```

4. **访问系统**
   - 在浏览器中访问 `http://your-domain.com`
   - 首次访问会显示登录界面

## 📱 功能介绍

### 基础功能（免费）

- **基础信息查询**: 查询个人基本信息，包括姓名、身份证号等
- **社交媒体查询**: 支持查询抖音、快手、微信等社交媒体账号信息
- **二要素核验**: 姓名和身份证号二要素验证
- **空号检测**: 检测手机号码是否为空号
- **身份证补齐**: 根据部分信息补全身份证号码

### VIP功能

- **网红猎魔**: 专业的网红信息查询工具
- **白底个户**: 获取个人详细档案信息
- **综合社工**: 综合性社工信息查询工具
- **地区猎魔**: 按地区查询相关信息
- **档案个户**: 查询个人档案详细信息
- **姓名猎魔**: 根据姓名查询相关信息
- **QQ绑定Phone查询**: 根据QQ号查询绑定的手机号信息

## 🔧 配置说明

### API配置

系统集成了上游API服务，主要配置在 `assets/js/config.js` 文件中：

```javascript
const CONFIG = {
    API: {
        UPSTREAM_BASE_URL: 'https://api.qnm6.top',
        ENDPOINTS: {
            UPSTREAM_REGISTER: '/admapi/zhuce.php',
            UPSTREAM_USER_INFO: '/admapi/demo.php',
            // ...其他端点
        }
    }
    // ...其他配置
};
```

### 用户数据存储

用户数据存储在 `api/auth/users.json` 文件中，包含以下字段：

- `username`: 用户名
- `password`: 加密后的密码
- `token`: 18位唯一标识符
- `created_at`: 注册时间
- `vipcode`: VIP状态码
- `viptime`: VIP到期时间

## 🎨 界面预览

### 桌面端
- 现代化的导航栏设计
- 卡片式工具展示
- 模态框交互界面

### 移动端
- 响应式布局适配
- 触摸友好的交互设计
- 移动端优化的菜单系统

## 🔒 安全特性

- **密码加密**: 使用PHP `password_hash()` 函数加密存储
- **Token验证**: 18位唯一Token用于身份验证
- **CORS配置**: 合理的跨域资源共享设置
- **文件保护**: .htaccess规则保护敏感文件
- **输入验证**: 严格的输入数据验证和过滤

## 📊 系统架构

```
idatas/
├── index.html              # 主页面
├── manifest.json           # PWA配置
├── robots.txt             # 搜索引擎配置
├── sitemap.xml            # 网站地图
├── test.html              # 测试页面
├── assets/                # 静态资源
│   ├── css/              # 样式文件
│   ├── js/               # JavaScript文件
│   └── images/           # 图片资源
└── api/                  # 后端API
    ├── auth/             # 用户认证
    └── tools/            # 工具API
```

## 🧪 测试

访问 `test.html` 页面进行系统功能测试：

- 基础系统测试
- 用户认证测试
- API接口测试
- 响应式设计测试
- 性能测试

## 📈 SEO优化

- 完整的meta标签配置
- Open Graph和Twitter Card支持
- 结构化数据（JSON-LD）
- 网站地图和robots.txt
- 语义化HTML结构

## 🔄 更新日志

### v1.0.0 (2025-08-02)
- 初始版本发布
- 完整的用户认证系统
- 基础工具功能实现
- 响应式设计完成
- SEO优化配置

## 📞 技术支持

如有问题或建议，请联系：

- 客服TG: @Riverkefu_bot
- 客服微信: kmhsg8
- 官方网站: https://www.idatariver.com

## 📄 许可证

本项目仅供学习和研究使用，请遵守相关法律法规。

---

**注意**: 本系统仅供合法用途使用，请遵守当地法律法规和隐私保护条例。
