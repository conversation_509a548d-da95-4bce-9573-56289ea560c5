// 工具管理系统 - 已清空所有功能工具
class ToolsManager {
    constructor() {
        this.tools = CONFIG.TOOLS || [];
        this.filteredTools = [...this.tools];
        this.currentFilter = 'all';
        this.searchQuery = '';
        this.init();
    }

    // 初始化工具管理器
    init() {
        this.renderTools();
        this.bindEvents();
    }

    // 绑定事件
    bindEvents() {
        // 工具卡片点击事件
        document.addEventListener('click', (e) => {
            const toolCard = e.target.closest('.tool-card');
            if (toolCard) {
                const toolId = toolCard.dataset.toolId;
                this.openTool(toolId);
            }
        });
    }

    // 渲染工具图标
    renderToolIcon(icon) {
        if (!icon) {
            return '<i class="fas fa-cog"></i>'; // 默认图标
        }

        // 检查是否为外部图片资源
        if (icon.startsWith('http://') ||
            icon.startsWith('https://') ||
            icon.startsWith('data:image/') ||
            icon.startsWith('/') ||
            icon.endsWith('.png') ||
            icon.endsWith('.jpg') ||
            icon.endsWith('.jpeg') ||
            icon.endsWith('.svg') ||
            icon.endsWith('.gif') ||
            icon.endsWith('.webp')) {
            return `<img src="${icon}" alt="工具图标" class="tool-icon-image" onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                    <i class="fas fa-cog" style="display: none;"></i>`;
        } else {
            // Font Awesome 图标
            return `<i class="${icon}"></i>`;
        }
    }

    // 渲染工具列表
    renderTools() {
        const toolsGrid = document.getElementById('toolsGrid');
        if (!toolsGrid) return;

        if (this.filteredTools.length === 0) {
            toolsGrid.innerHTML = `
                <div class="no-tools">
                    <i class="fas fa-search"></i>
                    <p>没有找到匹配的工具</p>
                </div>
            `;
            return;
        }

        toolsGrid.innerHTML = this.filteredTools.map(tool => `
            <div class="tool-card ${tool.vip ? 'vip' : ''}" data-tool-id="${tool.id}">
                <div class="tool-icon">
                    ${this.renderToolIcon(tool.icon)}
                </div>
                <h3 class="tool-name">${tool.name}</h3>
                <p class="tool-description">${tool.description}</p>
            </div>
        `).join('');
    }

    // 打开工具
    openTool(toolId) {
        const tool = this.tools.find(t => t.id === toolId);
        if (!tool) return;

        // 移除VIP权限检查，让上游API处理权限验证
        // 这样可以避免重复检查，提升用户体验

        // 设置模态框标题
        const modalTitle = document.getElementById('toolModalTitle');
        if (modalTitle) {
            modalTitle.textContent = tool.name;
        }

        // 渲染工具内容
        this.renderToolContent(tool);

        // 打开模态框
        if (window.UI) {
            window.UI.openModal('toolModal');
        } else {
            const modal = document.getElementById('toolModal');
            if (modal) {
                modal.classList.add('show');
                modal.style.display = 'flex';
            }
        }
    }

    // 渲染工具内容
    renderToolContent(tool) {
        const modalBody = document.getElementById('toolModalBody');
        if (!modalBody) return;

        modalBody.innerHTML = `
            <div class="tool-content">
                <div class="tool-description">
                    <p>${tool.description}</p>
                </div>
                <form class="tool-form" id="toolForm-${tool.id}">
                    ${this.renderToolFields(tool.fields)}
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                            开始查询
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="closeModal('toolModal')">
                            取消
                        </button>
                    </div>
                </form>
                <div class="result-container" id="result-${tool.id}" style="display: none;">
                    <h4>查询结果</h4>
                    <div class="result-content"></div>
                </div>
            </div>
        `;

        // 绑定表单提交事件
        const form = document.getElementById(`toolForm-${tool.id}`);
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleToolSubmit(tool, form);
            });
        }
    }

    // 渲染工具字段
    renderToolFields(fields) {
        return fields.map(field => {
            let fieldHtml = '';

            switch (field.type) {
                case 'text':
                    fieldHtml = `
                        <div class="form-group">
                            <label for="${field.name}">${field.label} ${field.required ? '*' : ''}</label>
                            <input type="text" id="${field.name}" name="${field.name}"
                                   placeholder="${field.placeholder || ''}"
                                   ${field.required ? 'required' : ''}>
                        </div>
                    `;
                    break;

                case 'select':
                    const options = field.options.map(opt =>
                        `<option value="${opt.value}">${opt.text || opt.label}</option>`
                    ).join('');
                    fieldHtml = `
                        <div class="form-group">
                            <label for="${field.name}">${field.label} ${field.required ? '*' : ''}</label>
                            <select id="${field.name}" name="${field.name}" ${field.required ? 'required' : ''}>
                                <option value="">请选择...</option>
                                ${options}
                            </select>
                        </div>
                    `;
                    break;

                case 'textarea':
                    fieldHtml = `
                        <div class="form-group">
                            <label for="${field.name}">${field.label} ${field.required ? '*' : ''}</label>
                            <textarea id="${field.name}" name="${field.name}" rows="3"
                                      placeholder="${field.placeholder || ''}"
                                      ${field.required ? 'required' : ''}></textarea>
                        </div>
                    `;
                    break;
            }

            return fieldHtml;
        }).join('');
    }

    // 处理工具表单提交
    async handleToolSubmit(tool, form) {
        const formData = new FormData(form);
        const data = {};

        // 收集表单数据
        for (let [key, value] of formData.entries()) {
            data[key] = value.trim();
        }

        // 验证必填字段
        const requiredFields = tool.fields.filter(f => f.required);
        for (let field of requiredFields) {
            if (!data[field.name]) {
                this.showToast(`请填写${field.label}`, 'error');
                return;
            }
        }

        // 特殊验证
        // 身份证库补齐功能允许部分身份证号，跳过标准验证
        if (data.idcard && tool.id !== 'idcard_complete' && !Utils.validateIdCard(data.idcard)) {
            this.showToast('身份证号格式不正确', 'error');
            return;
        }

        if (data.phone && !Utils.validatePhone(data.phone)) {
            this.showToast('手机号格式不正确', 'error');
            return;
        }

        if (data.qq && !Utils.validateQQ(data.qq)) {
            this.showToast('QQ号格式不正确，请输入5-11位数字', 'error');
            return;
        }

        this.showLoading('正在查询数据，请稍候...');

        try {
            const result = await this.callToolAPI(tool, data);
            this.displayResult(tool, result);
        } catch (error) {
            this.showToast(error.message || CONFIG.ERROR_MESSAGES.UNKNOWN_ERROR, 'error');
        } finally {
            this.hideLoading();
        }
    }

    // 调用工具API
    async callToolAPI(tool, data) {
        const token = window.AuthManager.getUserToken();
        // 由于有强制登录机制，这里不需要检查token

        // 添加token到请求数据
        data.token = token;

        // 添加查询类型参数
        if (tool.queryType) {
            data.lx = tool.queryType;
        }

        // 添加平台类型参数
        if (tool.platform) {
            data.platform = tool.platform;
        }

        let url = `${CONFIG.API.BASE_URL}${tool.endpoint}`;
        let options;

        // 卡泡聆听功能使用GET方法
        if (tool.id === 'kapao_listen') {
            // 构建GET请求的URL参数
            const params = new URLSearchParams(data);
            url += '?' + params.toString();

            options = {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            };
        } else {
            // 其他功能使用POST方法
            options = {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            };
        }

        const response = await fetch(url, options);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json();
    }

    // 显示查询结果
    displayResult(tool, result) {
        const resultContainer = document.getElementById(`result-${tool.id}`);
        if (!resultContainer) return;

        const resultContent = resultContainer.querySelector('.result-content');
        if (!resultContent) return;

        if (result.code === 200 || result.code === "200") {
            let content = '';

            // LSP每日自律结果 - 最高优先级
            if (result.action === 'get_video' && result.video) {
                const video = result.video;
                const videoId = 'lsp-video-' + Date.now();

                content += `<div class="lsp-daily-results">
                    <h5><strong>LSP每日自律视频：</strong></h5>

                    <div class="lsp-video-container">
                        <div class="lsp-video-player">
                            <video id="${videoId}" class="lsp-video" controls preload="metadata">
                                您的浏览器不支持视频播放
                            </video>
                        </div>

                        <div class="lsp-video-info">
                            <h6>视频信息</h6>
                            <div class="video-details">
                                <p><strong>视频名称：</strong>${video.name || '自律视频'}</p>
                                <p><strong>视频编号：</strong>${video.index || '未知'} / ${result.total_count || '未知'}</p>
                                <p><strong>获取时间：</strong>${video.selected_at || '未知'}</p>
                                <p><strong>播放格式：</strong>HLS (M3U8)</p>
                            </div>
                        </div>
                    </div>

                    <div class="lsp-actions">
                        <button onclick="refreshLSPVideo()" class="lsp-refresh-btn">
                            <i class="fas fa-sync-alt"></i>
                            换一个视频
                        </button>
                        <button onclick="copyToClipboard('${video.m3u8}')" class="lsp-copy-btn">
                            <i class="fas fa-copy"></i>
                            复制视频链接
                        </button>
                        <button onclick="openVideoInNewTab('${video.m3u8}')" class="lsp-external-btn">
                            <i class="fas fa-external-link-alt"></i>
                            新窗口播放
                        </button>
                    </div>

                    <div class="lsp-notice">
                        <i class="fas fa-heart"></i>
                        <span>每日自律，从观看正能量视频开始！坚持自律，成就更好的自己</span>
                    </div>
                </div>`;

                // 延迟初始化视频播放器
                setTimeout(() => {
                    initLSPVideoPlayer(videoId, video.m3u8);
                }, 100);
            }
            // LSP视频列表结果
            else if (result.action === 'get_list' && result.videos) {
                content += `<div class="lsp-list-results">
                    <h5><strong>LSP自律视频列表：</strong></h5>

                    <div class="lsp-list-info">
                        <p><strong>视频总数：</strong>${result.total_count || 0} 个</p>
                        <p><strong>更新时间：</strong>${new Date().toLocaleString()}</p>
                    </div>

                    <div class="lsp-video-list">`;

                result.videos.forEach((video, index) => {
                    content += `<div class="lsp-video-item">
                        <div class="video-item-info">
                            <h6>${video.name || '自律视频 ' + (index + 1)}</h6>
                            <p class="video-url">${video.m3u8}</p>
                        </div>
                        <div class="video-item-actions">
                            <button onclick="playLSPVideo('${video.m3u8}', '${video.name || '自律视频 ' + (index + 1)}')" class="play-btn">
                                <i class="fas fa-play"></i>
                                播放
                            </button>
                            <button onclick="copyToClipboard('${video.m3u8}')" class="copy-btn">
                                <i class="fas fa-copy"></i>
                                复制
                            </button>
                        </div>
                    </div>`;
                });

                content += `</div>

                    <div class="lsp-list-actions">
                        <button onclick="refreshLSPVideo()" class="lsp-random-btn">
                            <i class="fas fa-random"></i>
                            随机播放一个
                        </button>
                    </div>

                    <div class="lsp-notice">
                        <i class="fas fa-list"></i>
                        <span>以上是所有可用的自律视频，选择一个开始您的自律之旅</span>
                    </div>
                </div>`;
            }
            // IP信息查询结果 - 第二优先级
            else if (result.ip && result.data) {
                const ipData = result.data;

                content += `<div class="ip-query-results">
                    <h5><strong>IP信息查询成功：</strong></h5>

                    <div class="ip-info-container">
                        <div class="ip-basic-info">
                            <h6>基本信息</h6>
                            <div class="ip-info-grid">
                                <div class="info-item">
                                    <label>IP地址：</label>
                                    <span class="ip-address">${ipData.ip || result.ip}</span>
                                </div>
                                <div class="info-item">
                                    <label>国家：</label>
                                    <span>${ipData.country || '未知'} ${ipData.country_code ? '(' + ipData.country_code + ')' : ''}</span>
                                </div>
                                <div class="info-item">
                                    <label>地区：</label>
                                    <span>${ipData.region || '未知'}</span>
                                </div>
                                <div class="info-item">
                                    <label>城市：</label>
                                    <span>${ipData.city || '未知'}</span>
                                </div>
                                <div class="info-item">
                                    <label>邮编：</label>
                                    <span>${ipData.postal || '未知'}</span>
                                </div>
                                <div class="info-item">
                                    <label>时区：</label>
                                    <span>${ipData.timezone || '未知'}</span>
                                </div>
                            </div>
                        </div>

                        <div class="ip-location-info">
                            <h6>位置信息</h6>
                            <div class="ip-info-grid">
                                <div class="info-item">
                                    <label>经度：</label>
                                    <span>${ipData.longitude || '未知'}</span>
                                </div>
                                <div class="info-item">
                                    <label>纬度：</label>
                                    <span>${ipData.latitude || '未知'}</span>
                                </div>
                                ${ipData.latitude && ipData.longitude ? `
                                <div class="info-item full-width">
                                    <label>地图位置：</label>
                                    <a href="https://www.google.com/maps?q=${ipData.latitude},${ipData.longitude}" target="_blank" class="map-link">
                                        <i class="fas fa-map-marker-alt"></i>
                                        在Google地图中查看
                                    </a>
                                </div>` : ''}
                            </div>
                        </div>

                        <div class="ip-network-info">
                            <h6>网络信息</h6>
                            <div class="ip-info-grid">
                                <div class="info-item">
                                    <label>ISP：</label>
                                    <span>${ipData.isp || '未知'}</span>
                                </div>
                                <div class="info-item">
                                    <label>组织：</label>
                                    <span>${ipData.org || '未知'}</span>
                                </div>
                                <div class="info-item full-width">
                                    <label>AS信息：</label>
                                    <span>${ipData.as || '未知'}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="ip-actions">
                        <button onclick="copyToClipboard('${ipData.ip || result.ip}')" class="ip-copy-btn">
                            <i class="fas fa-copy"></i>
                            复制IP地址
                        </button>
                        <button onclick="queryAnotherIP()" class="ip-query-btn">
                            <i class="fas fa-search"></i>
                            查询其他IP
                        </button>
                        ${ipData.latitude && ipData.longitude ? `
                        <button onclick="copyToClipboard('${ipData.latitude},${ipData.longitude}')" class="ip-location-btn">
                            <i class="fas fa-map-pin"></i>
                            复制坐标
                        </button>` : ''}
                    </div>

                    <div class="ip-notice">
                        <i class="fas fa-info-circle"></i>
                        <span>IP信息来源：${ipData.api_source || '多个API源'}，数据仅供参考</span>
                    </div>
                </div>`;
            }
            // 买家秀欣赏结果 - 第二优先级
            else if (result.type === 'image' && (result.image_url || result.image_base64)) {
                const imageSrc = result.image_base64 || result.image_url;
                const downloadUrl = result.image_url || result.image_base64;

                content += `<div class="buyer-show-results">
                    <h5><strong>买家秀图片获取成功：</strong></h5>

                    <div class="buyer-show-container">
                        <div class="buyer-show-image-section">
                            <img src="${imageSrc}" alt="买家秀图片" class="buyer-show-image" />
                        </div>

                        <div class="buyer-show-info">
                            <h6>图片信息</h6>
                            <p><strong>类型：</strong>淘宝买家秀</p>
                            <p><strong>来源：</strong>随机获取</p>
                            <p><strong>格式：</strong>JPEG图片</p>
                        </div>
                    </div>

                    <div class="buyer-show-actions">
                        ${result.image_url ? `<a href="${result.image_url}" target="_blank" class="buyer-show-view-btn">
                            <i class="fas fa-external-link-alt"></i>
                            查看原图
                        </a>` : ''}
                        <button onclick="downloadBuyerShowImage('${downloadUrl}', '买家秀')" class="buyer-show-download-btn">
                            <i class="fas fa-download"></i>
                            下载图片
                        </button>
                        <button onclick="refreshBuyerShow('image')" class="buyer-show-refresh-btn">
                            <i class="fas fa-sync-alt"></i>
                            换一张
                        </button>
                    </div>

                    <div class="buyer-show-notice">
                        <i class="fas fa-info-circle"></i>
                        <span>图片来源于淘宝买家秀，仅供欣赏娱乐，请尊重他人隐私</span>
                    </div>
                </div>`;
            }
            // 买家秀JSON数据结果
            else if (result.type === 'json' && result.data) {
                content += `<div class="buyer-show-json-results">
                    <h5><strong>买家秀数据获取成功：</strong></h5>

                    <div class="buyer-show-json-container">
                        <div class="json-info">
                            <h6>数据信息</h6>
                            <p><strong>类型：</strong>JSON格式数据</p>
                            <p><strong>来源：</strong>淘宝买家秀API</p>
                        </div>

                        <div class="json-content">
                            <h6>详细数据</h6>
                            <pre class="json-display">${JSON.stringify(result.data, null, 2)}</pre>
                        </div>
                    </div>

                    <div class="buyer-show-actions">
                        <button onclick="copyToClipboard('${JSON.stringify(result.data, null, 2).replace(/'/g, "\\'")}', 'JSON数据')" class="buyer-show-copy-btn">
                            <i class="fas fa-copy"></i>
                            复制JSON
                        </button>
                        <button onclick="refreshBuyerShow('json')" class="buyer-show-refresh-btn">
                            <i class="fas fa-sync-alt"></i>
                            刷新数据
                        </button>
                    </div>

                    <div class="buyer-show-notice">
                        <i class="fas fa-info-circle"></i>
                        <span>数据来源于淘宝买家秀API，仅供学习和开发参考</span>
                    </div>
                </div>`;
            }
            // 二维码生成结果 - 第二优先级
            else if (result.generate_client_side) {
                const qrId = 'qr-' + Date.now();

                content += `<div class="qrcode-results">
                    <h5><strong>二维码生成成功：</strong></h5>

                    <div class="qrcode-container">
                        <div class="qrcode-image-section">
                            <div id="${qrId}" class="qrcode-placeholder"></div>
                        </div>

                        <div class="qrcode-info">
                            <h6>二维码信息</h6>
                            <p><strong>内容类型：</strong>${result.content_type}</p>
                            <p><strong>尺寸：</strong>${result.size}x${result.size} 像素</p>
                            <p><strong>纠错级别：</strong>${result.level_description}</p>
                            <div class="qrcode-content">
                                <strong>内容：</strong>
                                <div class="content-display">${result.content}</div>
                            </div>
                        </div>
                    </div>

                    <div class="qrcode-actions">
                        <button onclick="downloadQRCodeCanvas('${qrId}', '二维码')" class="qrcode-download-btn">
                            <i class="fas fa-download"></i>
                            下载二维码
                        </button>
                        <button onclick="copyToClipboard('${result.content}')" class="qrcode-copy-btn">
                            <i class="fas fa-copy"></i>
                            复制内容
                        </button>
                    </div>

                    <div class="qrcode-notice">
                        <i class="fas fa-info-circle"></i>
                        <span>二维码已生成完成，可以使用任意二维码扫描器进行扫描</span>
                    </div>
                </div>`;

                // 延迟生成二维码，确保DOM已渲染
                setTimeout(() => {
                    generateQRCodeWithJS(qrId, result.content, result.size, result.level);
                }, 100);
            }
            // 卡泡聆听音频结果 - 第二优先级
            else if (result.mp3url) {
                content += `<div class="audio-results">
                    <h5><strong>卡泡聆听结果：</strong></h5>

                    <div class="audio-container">
                        <div class="audio-player">
                            <audio controls preload="metadata" class="audio-element">
                                <source src="${result.mp3url}" type="audio/mpeg">
                                您的浏览器不支持音频播放。
                            </audio>
                        </div>

                        <div class="audio-info">
                            <p><strong>音频地址：</strong><span class="audio-url">${result.mp3url}</span></p>
                        </div>
                    </div>

                    <div class="audio-actions">
                        <a href="${result.mp3url}" target="_blank" class="audio-open-btn">
                            <i class="fas fa-external-link-alt"></i>
                            在新窗口打开
                        </a>
                        <button onclick="downloadAudio('${result.mp3url}')" class="audio-download-btn">
                            <i class="fas fa-download"></i>
                            下载音频
                        </button>
                    </div>

                    <div class="audio-notice">
                        <i class="fas fa-info-circle"></i>
                        <span>音频文件已获取成功，请及时保存。音频链接可能会在一段时间后失效。</span>
                    </div>
                </div>`;
            }
            // 身份证正反面查询结果 - 第二优先级
            else if (result.imgurl && result.back_imgurl) {
                const toolName = tool.name;
                content += `<div class="idcard-dual-results">
                    <h5><strong>${toolName}结果：</strong></h5>

                    <div class="idcard-images-container">
                        <div class="idcard-image-section">
                            <h6>身份证正面</h6>
                            <div class="image-container">
                                <img src="${result.back_imgurl}" alt="身份证正面" class="idcard-image" />
                            </div>
                            <div class="image-actions">
                                <a href="${result.back_imgurl}" target="_blank" class="image-view-btn">
                                    <i class="fas fa-external-link-alt"></i>
                                    查看原图
                                </a>
                                <button onclick="downloadImage('${result.back_imgurl}', '身份证正面')" class="image-download-btn">
                                    <i class="fas fa-download"></i>
                                    下载正面
                                </button>
                            </div>
                        </div>

                        <div class="idcard-image-section">
                            <h6>身份证反面</h6>
                            <div class="image-container">
                                <img src="${result.imgurl}" alt="身份证反面" class="idcard-image" />
                            </div>
                            <div class="image-actions">
                                <a href="${result.imgurl}" target="_blank" class="image-view-btn">
                                    <i class="fas fa-external-link-alt"></i>
                                    查看原图
                                </a>
                                <button onclick="downloadImage('${result.imgurl}', '身份证反面')" class="image-download-btn">
                                    <i class="fas fa-download"></i>
                                    下载反面
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="idcard-notice">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>身份证图片已生成完成，请及时保存。仅供合法用途使用，请勿用于违法活动。</span>
                    </div>
                </div>`;
            }
            // 个户图片结果（白底个户、档案个户）- 第三优先级
            else if (result.imgurl) {
                const toolName = tool.name;
                content += `<div class="image-results">
                    <h5><strong>${toolName}结果：</strong></h5>

                    <div class="image-container">
                        <img src="${result.imgurl}" alt="${toolName}图片" class="household-image" />
                    </div>

                    <div class="image-actions">
                        <a href="${result.imgurl}" target="_blank" class="image-view-btn">
                            <i class="fas fa-external-link-alt"></i>
                            查看原图
                        </a>
                        <button onclick="downloadImage('${result.imgurl}', '${toolName}')" class="image-download-btn">
                            <i class="fas fa-download"></i>
                            下载图片
                        </button>
                    </div>

                    <div class="image-notice">
                        <i class="fas fa-info-circle"></i>
                        <span>图片已生成完成，请及时保存。图片链接可能会在一段时间后失效。</span>
                    </div>
                </div>`;
            }
            // 新版照妖镜结果（数据在根级别）- 第二优先级
            else if (result.action_type) {
                if (result.action_type === 'create' && result.tpurl && result.pw) {
                    // 创建临时空间结果
                    content += `<div class="demon-mirror-create-results">
                        <h5><strong>照妖镜临时空间创建成功：</strong></h5>

                        <div class="demon-space-info">
                            <h6>临时空间信息</h6>
                            <div class="space-item">
                                <label>照妖链接：</label>
                                <div class="link-container">
                                    <input type="text" value="${result.tpurl}" readonly class="demon-link-input">
                                    <button onclick="copyToClipboard('${result.tpurl}')" class="copy-btn">
                                        <i class="fas fa-copy"></i> 复制
                                    </button>
                                </div>
                            </div>
                            <div class="space-item">
                                <label>查看密码：</label>
                                <div class="password-container">
                                    <span class="demon-password">${result.pw}</span>
                                    <button onclick="copyToClipboard('${result.pw}')" class="copy-btn">
                                        <i class="fas fa-copy"></i> 复制
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="demon-usage-guide">
                            <h6>使用说明</h6>
                            <ol>
                                <li>将照妖链接分享给目标用户</li>
                                <li>目标访问链接后会自动拍照</li>
                                <li>使用"查看拍摄图片"功能和密码查看结果</li>
                            </ol>
                        </div>

                        <div class="demon-notice">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span>需要HTTPS环境才能正常工作，目标首次访问需要授权摄像头权限</span>
                        </div>
                    </div>`;
                } else if (result.action_type === 'view' && result.imgurl) {
                    // 查看拍摄图片结果
                    const images = Array.isArray(result.imgurl) ? result.imgurl : [result.imgurl];
                    content += `<div class="demon-mirror-view-results">
                        <h5><strong>照妖镜拍摄结果：</strong></h5>

                        <div class="demon-stats">
                            <p><strong>拍摄次数：</strong>${result.count || images.length}</p>
                            <p><strong>图片数量：</strong>${images.length}</p>
                        </div>

                        <div class="demon-images-grid">`;

                    images.forEach((imageUrl, index) => {
                        content += `<div class="demon-image-item">
                            <h6>拍摄 ${index + 1}</h6>
                            <div class="image-container">
                                <img src="${imageUrl}" alt="拍摄图片 ${index + 1}" class="demon-image" />
                            </div>
                            <div class="image-actions">
                                <a href="${imageUrl}" target="_blank" class="image-view-btn">
                                    <i class="fas fa-external-link-alt"></i>
                                    查看原图
                                </a>
                                <button onclick="downloadImage('${imageUrl}', '照妖镜拍摄${index + 1}')" class="image-download-btn">
                                    <i class="fas fa-download"></i>
                                    下载图片
                                </button>
                            </div>
                        </div>`;
                    });

                    content += `</div>

                        <div class="demon-notice">
                            <i class="fas fa-info-circle"></i>
                            <span>所有数据不定期清理，请及时保存重要图片</span>
                        </div>
                    </div>`;
                } else {
                    // 其他情况显示原始数据
                    content += `<div class="demon-mirror-other">
                        <h5><strong>照妖镜操作结果：</strong></h5>
                        <pre class="result-text">${JSON.stringify(result, null, 2)}</pre>
                    </div>`;
                }
            }
            // 网红信息猎魔结果（数据在根级别）- 第三优先级
            else if (result.parsed_data && result.parsed_data.influencers) {
                const influencerData = result.parsed_data;

                content += `<div class="influencer-hunter-results">
                    <h5><strong>网红信息猎魔结果：</strong></h5>

                    <div class="influencer-summary">
                        <h6>查询摘要</h6>
                        <p><strong>找到网红数量：</strong>${influencerData.summary.total_count || 0}</p>`;

                if (influencerData.summary.platforms && influencerData.summary.platforms.length > 0) {
                    content += `<p><strong>涉及平台：</strong>${influencerData.summary.platforms.join(', ')}</p>`;
                }
                if (influencerData.summary.regions && influencerData.summary.regions.length > 0) {
                    content += `<p><strong>涉及地区：</strong>${influencerData.summary.regions.join(', ')}</p>`;
                }

                content += `</div>`;

                // 显示各个网红信息
                influencerData.influencers.forEach((influencer, index) => {
                    content += `<div class="influencer-card">
                        <h6><i class="fas fa-star"></i> 网红信息 ${index + 1}</h6>
                        <div class="influencer-content">`;

                    if (influencer.nickname) {
                        content += `<p><strong>昵称：</strong>${influencer.nickname}</p>`;
                    }
                    if (influencer.real_name) {
                        content += `<p><strong>真名：</strong>${influencer.real_name}</p>`;
                    }
                    if (influencer.idcard) {
                        content += `<p><strong>身份证：</strong>${influencer.idcard}</p>`;
                    }
                    if (influencer.phone) {
                        content += `<p><strong>电话：</strong>${influencer.phone}</p>`;
                    }
                    if (influencer.qq) {
                        content += `<p><strong>QQ号：</strong>${influencer.qq}</p>`;
                    }
                    if (influencer.weibo) {
                        content += `<p><strong>微博：</strong><a href="${influencer.weibo}" target="_blank">${influencer.weibo}</a></p>`;
                    }
                    if (influencer.address) {
                        content += `<p><strong>地址：</strong>${influencer.address}</p>`;
                    }
                    if (influencer.region) {
                        content += `<p><strong>地区：</strong>${influencer.region}</p>`;
                    }
                    if (influencer.platform) {
                        content += `<p><strong>平台：</strong>${influencer.platform}</p>`;
                    }

                    content += `</div></div>`;
                });

                content += `<div class="influencer-notice">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>网红信息仅供参考，请合法合规使用相关信息，尊重个人隐私</span>
                </div>`;

                content += `</div>`;
            }
            // 综合社工查询结果（数据在根级别）- 第三优先级
            else if (result.parsed_data && result.parsed_data.sections) {
                const socialData = result.parsed_data;

                content += `<div class="social-query-results">
                    <h5><strong>综合社工查询结果：</strong></h5>

                    <div class="social-summary">
                        <h6>查询摘要</h6>
                        <p><strong>数据块数量：</strong>${socialData.summary.total_sections || 0}</p>`;

                if (socialData.summary.key_info) {
                    const keyInfo = socialData.summary.key_info;
                    if (keyInfo.names && keyInfo.names.length > 0) {
                        content += `<p><strong>涉及姓名：</strong>${keyInfo.names.join(', ')}</p>`;
                    }
                    if (keyInfo.phones && keyInfo.phones.length > 0) {
                        content += `<p><strong>涉及手机号：</strong>${keyInfo.phones.join(', ')}</p>`;
                    }
                    if (keyInfo.idcards && keyInfo.idcards.length > 0) {
                        content += `<p><strong>涉及身份证：</strong>${keyInfo.idcards.join(', ')}</p>`;
                    }
                }

                content += `</div>`;

                // 显示各个数据块
                socialData.sections.forEach((section) => {
                    const sectionClass = getSectionClass(section.type);
                    const sectionIcon = getSectionIcon(section.type);

                    content += `<div class="social-section ${sectionClass}">
                        <h6><i class="${sectionIcon}"></i> ${section.title}</h6>
                        <div class="section-content">`;

                    if (Array.isArray(section.content)) {
                        section.content.forEach(item => {
                            content += `<p>${item}</p>`;
                        });
                    } else {
                        Object.entries(section.content).forEach(([key, value]) => {
                            content += `<p><strong>${key}：</strong>${value}</p>`;
                        });
                    }

                    content += `</div></div>`;
                });

                content += `<div class="social-notice">
                    <i class="fas fa-info-circle"></i>
                    <span>查询结果仅供参考，请合法合规使用相关信息</span>
                </div>`;

                content += `</div>`;
            }
            // 手机号状态检测结果（数据在根级别）- 第三优先级
            else if (result.parsed_data && result.parsed_data.phone) {
                const statusData = result.parsed_data;
                const statusClass = getStatusClass(statusData.status);
                const statusIcon = getStatusIcon(statusData.status);

                content += `<div class="phone-status-results ${statusClass}">
                    <h5><strong>手机号状态检测结果：</strong></h5>

                    <div class="phone-status-info">
                        <h6>检测信息</h6>
                        <p><strong>手机号：</strong>${statusData.phone}</p>`;

                if (statusData.status) {
                    content += `<div class="status-display">
                        <i class="${statusIcon}"></i>
                        <span class="status-text">${statusData.status}</span>
                    </div>`;
                }

                if (statusData.open_time) {
                    content += `<p><strong>开通时间：</strong>${statusData.open_time}</p>`;
                }

                content += `</div>`;

                // 状态说明
                content += `<div class="status-notice">
                    <i class="fas fa-info-circle"></i>
                    <span>检测结果仅供参考，实际状态以运营商为准</span>
                </div>`;

                content += `</div>`;
            }
            // 地区姓名猎魔结果（数据在根级别）- 第三优先级
            else if (result.parsed_data && Array.isArray(result.parsed_data)) {
                content += `<div class="hunt-results">
                    <h5><strong>猎魔查询结果：</strong></h5>
                    <div class="hunt-summary">
                        <span class="hunt-count">共找到 ${result.total_records || result.parsed_data.length} 条记录</span>
                        ${result.execution_time ? `<span class="hunt-time">查询耗时：${result.execution_time}</span>` : ''}
                    </div>`;

                result.parsed_data.forEach((record, index) => {
                    content += `<div class="hunt-record">
                        <h6>记录 ${index + 1}</h6>`;

                    if (record.name) {
                        content += `<p><strong>姓名：</strong>${record.name}</p>`;
                    }
                    if (record.idcard) {
                        content += `<p><strong>身份证：</strong>${record.idcard}</p>`;
                    }
                    if (record.phone) {
                        content += `<p><strong>电话：</strong>${record.phone}</p>`;
                    }
                    if (record.address && record.address.trim()) {
                        content += `<p><strong>地址：</strong>${record.address}</p>`;
                    }
                    if (record.data_source) {
                        content += `<p class="data-source"><strong>数据来源：</strong>${record.data_source}</p>`;
                    }

                    content += `</div>`;
                });

                content += `</div>`;
            }
            // QQ绑定Phone查询结果处理
            else if (tool.id === 'qq_phone_query' && result.shuju) {
                content += `<div class="qq-phone-results">
                    <h5><i class="fab fa-qq"></i> QQ绑定Phone查询结果</h5>
                    <div class="qq-phone-content">
                        <pre class="qq-phone-text">${result.shuju}</pre>
                    </div>`;

                // 显示执行时间
                if (result.execution_time) {
                    content += `<div class="query-time">
                        <i class="fas fa-clock"></i>
                        <span>查询耗时：${result.execution_time}</span>
                    </div>`;
                }

                content += `</div>`;
            }
            // 显示原始猎魔数据（如果有shuju字段但解析失败）
            else if (result.shuju && !result.parsed_data) {
                content += `<div class="raw-hunt-data">
                    <h5><strong>原始查询结果：</strong></h5>
                    <pre class="hunt-raw-text">${result.shuju}</pre>
                </div>`;
            }
            // 其他API的标准结果处理
            else if (result.data) {
                // 处理不同类型的结果
                if (result.data.image_url) {
                    const imageId = 'result-image-' + Date.now();
                    const useSkeletonEffect = Math.random() > 0.5; // 随机选择加载效果

                    if (useSkeletonEffect) {
                        // 使用骨架屏效果
                        content += `<div class="result-image-container" id="container-${imageId}">
                            <div class="image-skeleton" id="skeleton-${imageId}"></div>
                            <img src="${result.data.image_url}" alt="查询结果" class="result-image" id="${imageId}" style="display: none;">
                        </div>`;
                    } else {
                        // 使用加载动画效果
                        content += `<div class="result-image-container" id="container-${imageId}">
                            <div class="image-loading-placeholder" id="placeholder-${imageId}">
                                <div class="image-loading-spinner"></div>
                                <div class="image-loading-text">正在加载图片...</div>
                            </div>
                            <img src="${result.data.image_url}" alt="查询结果" class="result-image" id="${imageId}" style="display: none;">
                        </div>`;
                    }

                    // 延迟执行图片加载处理
                    setTimeout(() => {
                        this.handleImageLoading(imageId, result.data.image_url, useSkeletonEffect);
                    }, 100);
                }

                if (result.data.name) {
                    content += `<p><strong>姓名：</strong>${result.data.name}</p>`;
                }

                if (result.data.idcard) {
                    content += `<p><strong>身份证号：</strong>${result.data.idcard}</p>`;
                }

                if (result.data.fake_address) {
                    content += `<p><strong>详细地址：</strong>${result.data.fake_address}</p>`;
                }

                if (result.data.real_address) {
                    content += `<p><strong>三级地址：</strong>${result.data.real_address}</p>`;
                }

                // 社交平台反查结果
                if (result.data.platform) {
                    const platformNames = {
                        'douyin': '抖音',
                        'kuaishou': '快手',
                        'wechat': '微信'
                    };
                    content += `<p><strong>查询平台：</strong>${platformNames[result.data.platform] || result.data.platform}</p>`;
                }

                if (result.data.account) {
                    content += `<p><strong>账号：</strong>${result.data.account}</p>`;
                }

                // 婚姻史查询结果
                if (result.data.婚姻状况) {
                    content += `<p><strong>婚姻状况：</strong>${result.data.婚姻状况}</p>`;
                }

                if (result.data.婚姻次数 !== undefined) {
                    content += `<p><strong>婚姻次数：</strong>${result.data.婚姻次数}次</p>`;
                }

                if (result.data.婚姻史 && Array.isArray(result.data.婚姻史)) {
                    content += `<div class="marriage-history">
                        <h5><strong>婚姻史详情：</strong></h5>`;

                    result.data.婚姻史.forEach((marriage, index) => {
                        content += `<div class="marriage-record">
                            <h6>第${index + 1}段婚姻</h6>
                            <p><strong>配偶姓名：</strong>${marriage.配偶姓名}</p>
                            <p><strong>登记时间：</strong>${marriage.登记时间}</p>
                            <p><strong>离婚时间：</strong>${marriage.离婚时间}</p>
                            <p><strong>离婚原因：</strong>${marriage.离婚原因}</p>
                            <p><strong>子女情况：</strong>${marriage.子女情况}</p>
                        </div>`;
                    });

                    content += `</div>`;
                }

                // 家庭成员查询结果（数组格式）
                if (Array.isArray(result.data) && result.data.length > 0) {
                    content += `<div class="family-members">
                        <h5><strong>家庭成员信息：</strong></h5>`;

                    result.data.forEach((member, index) => {
                        content += `<div class="family-member">
                            <h6>成员 ${index + 1}</h6>
                            <p><strong>姓名：</strong>${member.name || '未知'}</p>
                            <p><strong>身份证号：</strong>${member.idcard || '未知'}</p>`;

                        // 显示其他可能的字段
                        Object.keys(member).forEach(key => {
                            if (key !== 'name' && key !== 'idcard' && member[key]) {
                                const fieldNames = {
                                    'relation': '关系',
                                    'gender': '性别',
                                    'age': '年龄',
                                    'phone': '电话',
                                    'address': '地址',
                                    'occupation': '职业'
                                };
                                const fieldName = fieldNames[key] || key;
                                content += `<p><strong>${fieldName}：</strong>${member[key]}</p>`;
                            }
                        });

                        content += `</div>`;
                    });

                    content += `</div>`;
                }

                // 实时定位结果
                if (result.data.lat && result.data.lng) {
                    content += `<div class="location-results">
                        <h5><strong>实时定位信息：</strong></h5>`;

                    // 显示API消息（如果包含特殊提示）
                    if (result.message && result.message.includes('仅供参考')) {
                        content += `<div class="location-notice">
                            <i class="fas fa-info-circle"></i>
                            <span>${result.message}</span>
                        </div>`;
                    }

                    // 基本位置信息
                    content += `<div class="location-basic">
                        <h6>位置信息</h6>`;

                    if (result.data.province) {
                        content += `<p><strong>省份：</strong>${result.data.province}</p>`;
                    }
                    if (result.data.city) {
                        content += `<p><strong>城市：</strong>${result.data.city}</p>`;
                    }
                    if (result.data.address) {
                        content += `<p><strong>详细地址：</strong>${result.data.address}</p>`;
                    }
                    if (result.data.operator && result.data.operator.trim()) {
                        content += `<p><strong>运营商：</strong>${result.data.operator}</p>`;
                    }

                    content += `</div>`;

                    // GPS坐标信息
                    content += `<div class="location-coordinates">
                        <h6>GPS坐标</h6>
                        <p><strong>纬度：</strong>${result.data.lat}</p>
                        <p><strong>经度：</strong>${result.data.lng}</p>`;

                    if (result.data.altitude) {
                        content += `<p><strong>海拔：</strong>${result.data.altitude}</p>`;
                    }
                    if (result.data.gps_accuracy) {
                        content += `<p><strong>GPS精度：</strong>${result.data.gps_accuracy}</p>`;
                    }

                    content += `</div>`;

                    // 技术信息
                    content += `<div class="location-technical">
                        <h6>技术信息</h6>`;

                    if (result.data.signal_strength) {
                        content += `<p><strong>信号强度：</strong>${result.data.signal_strength}</p>`;
                    }
                    if (result.data.network_type) {
                        content += `<p><strong>网络类型：</strong>${result.data.network_type}</p>`;
                    }
                    if (result.data.cell_tower_id) {
                        content += `<p><strong>基站ID：</strong>${result.data.cell_tower_id}</p>`;
                    }
                    if (result.data.location_type) {
                        content += `<p><strong>定位类型：</strong>${result.data.location_type}</p>`;
                    }

                    content += `</div>`;

                    // 设备状态
                    if (result.data.battery_level || result.data.speed || result.data.heading) {
                        content += `<div class="location-device">
                            <h6>设备状态</h6>`;

                        if (result.data.battery_level) {
                            content += `<p><strong>电池电量：</strong>${result.data.battery_level}</p>`;
                        }
                        if (result.data.speed) {
                            content += `<p><strong>移动速度：</strong>${result.data.speed}</p>`;
                        }
                        if (result.data.heading) {
                            content += `<p><strong>方向角度：</strong>${result.data.heading}</p>`;
                        }

                        content += `</div>`;
                    }

                    // 时间信息
                    if (result.data.last_update || result.data.timestamp) {
                        content += `<div class="location-time">
                            <h6>时间信息</h6>`;

                        if (result.data.last_update) {
                            content += `<p><strong>最后更新：</strong>${result.data.last_update}</p>`;
                        }
                        if (result.data.timestamp) {
                            const date = new Date(result.data.timestamp * 1000);
                            const timeStr = date.toLocaleString('zh-CN', {
                                year: 'numeric',
                                month: '2-digit',
                                day: '2-digit',
                                hour: '2-digit',
                                minute: '2-digit',
                                second: '2-digit'
                            });
                            content += `<p><strong>时间戳：</strong>${result.data.timestamp} (${timeStr})</p>`;
                        }

                        content += `</div>`;
                    }

                    // 地图链接
                    if (result.data.bing_map_url) {
                        content += `<div class="location-map">
                            <h6>地图查看</h6>
                            <a href="${result.data.bing_map_url}" target="_blank" class="map-link">
                                <i class="fas fa-external-link-alt"></i>
                                在必应地图中查看
                            </a>
                        </div>`;
                    }

                    content += `</div>`;
                }

                // 二要素核验结果
                if (result.data && result.data.verify_result) {
                    const isSuccess = result.data.verify_result === '核验通过';
                    const statusClass = isSuccess ? 'verify-success' : 'verify-failed';
                    const statusIcon = isSuccess ? 'fas fa-check-circle' : 'fas fa-times-circle';
                    const statusText = isSuccess ? '核验通过' : '核验失败';

                    content += `<div class="verify-results ${statusClass}">
                        <h5><strong>二要素核验结果：</strong></h5>

                        <div class="verify-status">
                            <i class="${statusIcon}"></i>
                            <span class="verify-text">${statusText}</span>
                        </div>

                        <div class="verify-details">
                            <h6>核验信息</h6>
                            <p><strong>姓名：</strong>${result.data.name}</p>
                            <p><strong>身份证号：</strong>${result.data.idcard}</p>
                            <p><strong>核验消息：</strong>${result.data.original_message}</p>
                        </div>

                        <div class="verify-notice">
                            <i class="fas fa-info-circle"></i>
                            <span>核验结果基于权威数据源，24小时人工轮班检测确保准确性</span>
                        </div>
                    </div>`;
                }

                // 智慧机主查询结果
                if (result.data && result.data.phone && result.data.name) {
                    content += `<div class="owner-results">
                        <h5><strong>智慧机主查询结果：</strong></h5>

                        <div class="owner-basic">
                            <h6>基本信息</h6>
                            <p><strong>手机号：</strong>${result.data.phone}</p>
                            <p><strong>姓名：</strong>${result.data.name}</p>`;

                    if (result.data.idcard) {
                        content += `<p><strong>身份证号：</strong>${result.data.idcard}</p>`;
                    }
                    if (result.data.address) {
                        content += `<p><strong>地址：</strong>${result.data.address}</p>`;
                    }

                    content += `</div>`;

                    // 归属地信息
                    if (result.data.location) {
                        content += `<div class="owner-location">
                            <h6>归属地信息</h6>`;

                        if (result.data.location.province) {
                            content += `<p><strong>省份：</strong>${result.data.location.province}</p>`;
                        }
                        if (result.data.location.city) {
                            content += `<p><strong>城市：</strong>${result.data.location.city}</p>`;
                        }
                        if (result.data.location.sp) {
                            content += `<p><strong>运营商：</strong>${result.data.location.sp}</p>`;
                        }
                        if (result.data.location.postcode) {
                            content += `<p><strong>邮编：</strong>${result.data.location.postcode}</p>`;
                        }
                        if (result.data.location.tel_prefix) {
                            content += `<p><strong>区号：</strong>${result.data.location.tel_prefix}</p>`;
                        }

                        content += `</div>`;
                    }

                    content += `</div>`;
                }

                // 身份证库补齐结果（数据在根级别）
                if (result.parsed_data && Array.isArray(result.parsed_data)) {
                    content += `<div class="idcard-complete-results">
                        <h5><strong>身份证库补齐结果：</strong></h5>
                        <div class="idcard-summary">
                            <span class="idcard-count">共找到 ${result.total_records || result.parsed_data.length} 条匹配记录</span>
                        </div>`;

                    result.parsed_data.forEach((record, index) => {
                        content += `<div class="idcard-record">
                            <h6>记录 ${index + 1}</h6>
                            <p><strong>姓名：</strong>${record.name}</p>
                            <p><strong>身份证号：</strong>${record.idcard}</p>
                        </div>`;
                    });

                    content += `</div>`;
                }
                // 显示原始身份证补齐数据（如果有msg字段但解析失败）
                else if (result.msg && !result.parsed_data) {
                    content += `<div class="raw-idcard-data">
                        <h5><strong>原始补齐结果：</strong></h5>
                        <pre class="idcard-raw-text">${result.msg}</pre>
                    </div>`;
                }
                // 显示原始手机号状态数据（如果有message字段但解析失败）
                else if (result.message && !result.parsed_data && result.message.includes('手机号:')) {
                    content += `<div class="raw-phone-status-data">
                        <h5><strong>原始检测结果：</strong></h5>
                        <pre class="phone-status-raw-text">${result.message}</pre>
                    </div>`;
                }

                // 显示原始综合社工查询数据（如果有shuju字段但解析失败）
                if (result.shuju && !result.parsed_data) {
                    content += `<div class="raw-social-data">
                        <h5><strong>原始查询结果：</strong></h5>
                        <pre class="social-raw-text">${result.shuju}</pre>
                    </div>`;
                }

                // 如果没有特定字段，显示原始数据
                if (!content) {
                    content = `<pre class="result-text">${JSON.stringify(result.data, null, 2)}</pre>`;
                }
            } else {
                content = '<p>查询完成，但没有返回数据</p>';
            }

            resultContent.innerHTML = content;

            // 如果有图片，显示特殊的成功提示
            if (result.data && result.data.image_url) {
                this.showToast('查询成功！图片正在加载中...', 'success');
            } else {
                this.showToast(CONFIG.SUCCESS_MESSAGES.QUERY_SUCCESS, 'success');
            }

            // 触发工具使用跟踪事件
            document.dispatchEvent(new CustomEvent('toolUsageTracked', {
                detail: {
                    toolName: tool.name,
                    toolId: tool.id,
                    timestamp: Date.now()
                }
            }));
        } else {
            // 处理不同类型的错误
            let errorMessage = result.message || '查询失败';
            let toastType = 'error';

            // 检查是否是权限相关错误
            if (result.code === 403 || result.code === 410) {
                toastType = 'warning';
                if (result.message && result.message.includes('会员')) {
                    errorMessage = result.message;
                }
            }

            resultContent.innerHTML = `<p class="error-message">${errorMessage}</p>`;
            this.showToast(errorMessage, toastType);
        }

        resultContainer.style.display = 'block';
    }

    // 处理图片加载
    handleImageLoading(imageId, imageUrl, useSkeletonEffect = false) {
        const img = document.getElementById(imageId);
        const placeholder = document.getElementById(`placeholder-${imageId}`);
        const skeleton = document.getElementById(`skeleton-${imageId}`);
        const container = document.getElementById(`container-${imageId}`);

        if (!img || !container) return;

        // 创建新的图片对象来预加载
        const preloadImg = new Image();

        // 设置加载超时
        const loadingTimeout = setTimeout(() => {
            this.handleImageError(imageId, imageUrl, '图片加载超时');
        }, 15000); // 15秒超时

        preloadImg.onload = () => {
            clearTimeout(loadingTimeout);

            // 图片加载成功
            img.src = preloadImg.src;
            img.style.display = 'block';

            // 显示图片加载完成提示
            if (window.UI) {
                window.UI.showToast('图片加载完成', 'success');
            }

            // 添加淡入效果
            setTimeout(() => {
                img.classList.add('loaded');

                if (useSkeletonEffect && skeleton) {
                    skeleton.style.opacity = '0';
                    skeleton.style.transition = 'opacity 0.3s ease-out';
                    setTimeout(() => skeleton.remove(), 300);
                } else if (placeholder) {
                    placeholder.classList.add('hidden');
                    setTimeout(() => placeholder.remove(), 350);
                }
            }, 50);
        };

        preloadImg.onerror = () => {
            clearTimeout(loadingTimeout);
            this.handleImageError(imageId, imageUrl, '图片加载失败');
        };

        // 开始加载图片
        preloadImg.src = imageUrl;
    }

    // 处理图片加载错误
    handleImageError(imageId, imageUrl, errorMessage) {
        const container = document.getElementById(`container-${imageId}`);
        if (!container) return;

        container.innerHTML = `
            <div class="image-error">
                <i class="fas fa-image"></i>
                <div class="image-error-text">
                    ${errorMessage}<br>
                    <small>请检查网络连接或稍后重试</small>
                </div>
                <button class="image-retry-btn" onclick="window.ToolsManager.retryImageLoad('${imageId}', '${imageUrl}')">
                    <i class="fas fa-redo"></i> 重新加载
                </button>
            </div>
        `;
    }

    // 重试图片加载
    retryImageLoad(imageId, imageUrl) {
        const container = document.getElementById(`container-${imageId}`);
        if (!container) return;

        // 重置容器内容
        container.innerHTML = `
            <div class="image-loading-placeholder" id="placeholder-${imageId}">
                <div class="image-loading-spinner"></div>
                <div class="image-loading-text">正在重新加载图片...</div>
            </div>
            <img src="" alt="查询结果" class="result-image" id="${imageId}" style="display: none;">
        `;

        // 重新开始加载
        setTimeout(() => {
            this.handleImageLoading(imageId, imageUrl);
        }, 100);
    }

    // 按类别过滤工具
    filterByCategory(category) {
        this.currentFilter = category;
        this.applyFilters();
    }

    // 按搜索词过滤工具
    filterTools(query) {
        this.searchQuery = query.toLowerCase();
        this.applyFilters();
    }

    // 应用过滤器
    applyFilters() {
        this.filteredTools = this.tools.filter(tool => {
            // 类别过滤
            let categoryMatch = true;
            if (this.currentFilter !== 'all') {
                if (this.currentFilter === 'basic') {
                    categoryMatch = !tool.vip;
                } else if (this.currentFilter === 'vip') {
                    categoryMatch = tool.vip;
                }
            }

            // 搜索过滤
            let searchMatch = true;
            if (this.searchQuery) {
                searchMatch = tool.name.toLowerCase().includes(this.searchQuery) ||
                             tool.description.toLowerCase().includes(this.searchQuery);
            }

            return categoryMatch && searchMatch;
        });

        this.renderTools();
    }

    // 获取工具统计
    getToolStats() {
        const total = this.tools.length;
        const basic = this.tools.filter(t => !t.vip).length;
        const vip = this.tools.filter(t => t.vip).length;

        return { total, basic, vip };
    }

    // UI辅助方法
    showLoading(message = '加载中...') {
        if (window.UI) {
            window.UI.showLoading(message);
        }
    }

    hideLoading() {
        if (window.UI) {
            window.UI.hideLoading();
        }
    }

    showToast(message, type = 'info') {
        if (window.UI) {
            window.UI.showToast(message, type);
        } else {
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    }
}

// 下载图片函数
function downloadImage(imageUrl, toolName) {
    try {
        // 创建一个临时的a标签来触发下载
        const link = document.createElement('a');
        link.href = imageUrl;
        link.download = `${toolName}-${new Date().getTime()}.png`;
        link.target = '_blank';

        // 添加到DOM并点击
        document.body.appendChild(link);
        link.click();

        // 清理
        document.body.removeChild(link);

        // 显示提示
        if (window.ToolsManager) {
            window.ToolsManager.showToast('开始下载图片', 'success');
        }
    } catch (error) {
        console.error('下载图片失败:', error);
        if (window.ToolsManager) {
            window.ToolsManager.showToast('下载失败，请手动保存图片', 'error');
        }
    }
}

// 获取手机号状态对应的CSS类
function getStatusClass(status) {
    if (!status) return 'status-unknown';

    const statusLower = status.toLowerCase();
    if (statusLower.includes('空号') || statusLower.includes('停机') || statusLower.includes('注销')) {
        return 'status-inactive';
    } else if (statusLower.includes('正常') || statusLower.includes('在网')) {
        return 'status-active';
    } else {
        return 'status-unknown';
    }
}

// 获取手机号状态对应的图标
function getStatusIcon(status) {
    if (!status) return 'fas fa-question-circle';

    const statusLower = status.toLowerCase();
    if (statusLower.includes('空号') || statusLower.includes('停机') || statusLower.includes('注销')) {
        return 'fas fa-times-circle';
    } else if (statusLower.includes('正常') || statusLower.includes('在网')) {
        return 'fas fa-check-circle';
    } else {
        return 'fas fa-question-circle';
    }
}

// 下载音频函数
function downloadAudio(audioUrl) {
    try {
        // 创建一个临时的a标签来触发下载
        const link = document.createElement('a');
        link.href = audioUrl;
        link.download = `卡泡聆听-${new Date().getTime()}.mp3`;
        link.target = '_blank';

        // 添加到DOM并点击
        document.body.appendChild(link);
        link.click();

        // 清理
        document.body.removeChild(link);

        // 显示提示
        if (window.ToolsManager) {
            window.ToolsManager.showToast('开始下载音频', 'success');
        }
    } catch (error) {
        console.error('下载音频失败:', error);
        if (window.ToolsManager) {
            window.ToolsManager.showToast('下载失败，请手动保存音频', 'error');
        }
    }
}

// 获取社工查询数据块对应的CSS类
function getSectionClass(type) {
    switch (type) {
        case 'data_label':
            return 'section-data-label';
        case 'smart_analysis':
            return 'section-smart-analysis';
        case 'related_info':
            return 'section-related-info';
        default:
            return 'section-other';
    }
}

// 获取社工查询数据块对应的图标
function getSectionIcon(type) {
    switch (type) {
        case 'data_label':
            return 'fas fa-tag';
        case 'smart_analysis':
            return 'fas fa-brain';
        case 'related_info':
            return 'fas fa-link';
        default:
            return 'fas fa-info-circle';
    }
}

// 复制到剪贴板函数
function copyToClipboard(text) {
    try {
        // 使用现代的Clipboard API
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(text).then(() => {
                if (window.ToolsManager) {
                    window.ToolsManager.showToast('已复制到剪贴板', 'success');
                }
            }).catch(err => {
                console.error('复制失败:', err);
                fallbackCopyTextToClipboard(text);
            });
        } else {
            // 降级方案
            fallbackCopyTextToClipboard(text);
        }
    } catch (error) {
        console.error('复制失败:', error);
        if (window.ToolsManager) {
            window.ToolsManager.showToast('复制失败，请手动复制', 'error');
        }
    }
}

// 降级复制方案
function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.top = '0';
    textArea.style.left = '0';
    textArea.style.position = 'fixed';
    textArea.style.opacity = '0';

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        const successful = document.execCommand('copy');
        if (successful && window.ToolsManager) {
            window.ToolsManager.showToast('已复制到剪贴板', 'success');
        } else if (window.ToolsManager) {
            window.ToolsManager.showToast('复制失败，请手动复制', 'error');
        }
    } catch (err) {
        console.error('降级复制失败:', err);
        if (window.ToolsManager) {
            window.ToolsManager.showToast('复制失败，请手动复制', 'error');
        }
    }

    document.body.removeChild(textArea);
}

// 使用qrcode.js生成二维码
function generateQRCodeWithJS(containerId, content, size, level) {
    try {
        const container = document.getElementById(containerId);
        if (!container) {
            console.error('二维码容器未找到:', containerId);
            return;
        }

        // 清空容器
        container.innerHTML = '';

        // 映射纠错级别
        const errorCorrectionLevel = {
            'L': 'L',
            'M': 'M',
            'Q': 'Q',
            'H': 'H'
        }[level] || 'M';

        // 生成二维码
        const qr = new QRCode(container, {
            text: content,
            width: parseInt(size) || 200,
            height: parseInt(size) || 200,
            colorDark: '#000000',
            colorLight: '#ffffff',
            correctLevel: QRCode.CorrectLevel[errorCorrectionLevel]
        });

        console.log('二维码生成成功:', containerId);

    } catch (error) {
        console.error('二维码生成失败:', error);
        const container = document.getElementById(containerId);
        if (container) {
            container.innerHTML = '<div class="qr-error">二维码生成失败</div>';
        }
    }
}

// 下载二维码Canvas
function downloadQRCodeCanvas(containerId, filename) {
    try {
        const container = document.getElementById(containerId);
        if (!container) {
            console.error('二维码容器未找到:', containerId);
            return;
        }

        const canvas = container.querySelector('canvas');
        if (!canvas) {
            console.error('二维码Canvas未找到');
            if (window.ToolsManager) {
                window.ToolsManager.showToast('二维码未生成，无法下载', 'error');
            }
            return;
        }

        // 将Canvas转换为Blob并下载
        canvas.toBlob(function(blob) {
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `${filename}-${new Date().getTime()}.png`;

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // 清理URL对象
            URL.revokeObjectURL(link.href);

            if (window.ToolsManager) {
                window.ToolsManager.showToast('开始下载二维码', 'success');
            }
        }, 'image/png');

    } catch (error) {
        console.error('下载二维码失败:', error);
        if (window.ToolsManager) {
            window.ToolsManager.showToast('下载失败，请手动保存二维码', 'error');
        }
    }
}

// 下载买家秀图片函数
function downloadBuyerShowImage(imageData, filename) {
    try {
        const link = document.createElement('a');

        if (imageData.startsWith('data:')) {
            // Base64格式
            link.href = imageData;
        } else {
            // URL格式
            link.href = imageData;
        }

        link.download = `${filename}-${new Date().getTime()}.jpg`;
        link.target = '_blank';

        // 添加到DOM并点击
        document.body.appendChild(link);
        link.click();

        // 清理
        document.body.removeChild(link);

        // 显示提示
        if (window.ToolsManager) {
            window.ToolsManager.showToast('开始下载买家秀图片', 'success');
        }
    } catch (error) {
        console.error('下载买家秀图片失败:', error);
        if (window.ToolsManager) {
            window.ToolsManager.showToast('下载失败，请手动保存图片', 'error');
        }
    }
}

// 刷新买家秀函数
function refreshBuyerShow(type) {
    try {
        // 找到当前工具
        const currentTool = window.currentTool;
        if (!currentTool) {
            console.error('当前工具未找到');
            return;
        }

        // 构建请求数据
        const formData = { type: type };

        // 显示加载状态
        if (window.ToolsManager) {
            window.ToolsManager.showToast('正在获取新的买家秀...', 'info');
        }

        // 重新调用API
        window.ToolsManager.callToolAPI(currentTool, formData);

    } catch (error) {
        console.error('刷新买家秀失败:', error);
        if (window.ToolsManager) {
            window.ToolsManager.showToast('刷新失败，请重试', 'error');
        }
    }
}

// 查询其他IP函数
function queryAnotherIP() {
    try {
        const newIP = prompt('请输入要查询的IP地址：');
        if (!newIP) {
            return;
        }

        // 简单的IP格式验证
        const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
        if (!ipRegex.test(newIP)) {
            if (window.ToolsManager) {
                window.ToolsManager.showToast('IP地址格式不正确', 'error');
            }
            return;
        }

        // 找到IP查询工具
        const ipTool = window.CONFIG.TOOLS.find(tool => tool.id === 'ip_query');
        if (!ipTool) {
            console.error('IP查询工具未找到');
            return;
        }

        // 构建请求数据
        const formData = { ip: newIP };

        // 显示加载状态
        if (window.ToolsManager) {
            window.ToolsManager.showToast('正在查询IP信息...', 'info');
        }

        // 调用API
        window.ToolsManager.callToolAPI(ipTool, formData);

    } catch (error) {
        console.error('查询其他IP失败:', error);
        if (window.ToolsManager) {
            window.ToolsManager.showToast('查询失败，请重试', 'error');
        }
    }
}

// LSP视频播放器相关函数
let currentHLS = null;

// 初始化LSP视频播放器
function initLSPVideoPlayer(videoId, m3u8Url) {
    try {
        const videoElement = document.getElementById(videoId);
        if (!videoElement) {
            console.error('视频元素未找到:', videoId);
            return;
        }

        // 清理之前的HLS实例
        if (currentHLS) {
            currentHLS.destroy();
            currentHLS = null;
        }

        if (window.Hls && Hls.isSupported()) {
            // 使用HLS.js
            currentHLS = new Hls({
                enableWorker: true,
                lowLatencyMode: false,
                backBufferLength: 90
            });

            currentHLS.loadSource(m3u8Url);
            currentHLS.attachMedia(videoElement);

            currentHLS.on(Hls.Events.MANIFEST_PARSED, function() {
                console.log('HLS manifest parsed, ready to play');
            });

            currentHLS.on(Hls.Events.ERROR, function(event, data) {
                console.error('HLS error:', data);
                if (data.fatal) {
                    switch(data.type) {
                        case Hls.ErrorTypes.NETWORK_ERROR:
                            console.log('网络错误，尝试恢复...');
                            currentHLS.startLoad();
                            break;
                        case Hls.ErrorTypes.MEDIA_ERROR:
                            console.log('媒体错误，尝试恢复...');
                            currentHLS.recoverMediaError();
                            break;
                        default:
                            console.error('无法恢复的错误，销毁播放器');
                            currentHLS.destroy();
                            currentHLS = null;
                            break;
                    }
                }
            });

        } else if (videoElement.canPlayType('application/vnd.apple.mpegurl')) {
            // Safari原生支持
            videoElement.src = m3u8Url;
        } else {
            console.error('浏览器不支持HLS播放');
            videoElement.innerHTML = '<p style="color: red; text-align: center; padding: 20px;">您的浏览器不支持HLS视频播放</p>';
        }

    } catch (error) {
        console.error('初始化视频播放器失败:', error);
    }
}

// 刷新LSP视频
function refreshLSPVideo() {
    try {
        // 找到LSP自律工具
        const lspTool = window.CONFIG.TOOLS.find(tool => tool.id === 'lsp_daily');
        if (!lspTool) {
            console.error('LSP自律工具未找到');
            return;
        }

        // 构建请求数据
        const formData = { action: 'get_video' };

        // 显示加载状态
        if (window.ToolsManager) {
            window.ToolsManager.showToast('正在获取新的自律视频...', 'info');
        }

        // 调用API
        window.ToolsManager.callToolAPI(lspTool, formData);

    } catch (error) {
        console.error('刷新LSP视频失败:', error);
        if (window.ToolsManager) {
            window.ToolsManager.showToast('刷新失败，请重试', 'error');
        }
    }
}

// 播放指定的LSP视频
function playLSPVideo(m3u8Url, videoName) {
    try {
        // 创建新的视频播放器
        const videoId = 'lsp-video-' + Date.now();
        const videoHTML = `
            <div class="lsp-video-player-popup">
                <div class="video-popup-header">
                    <h6>${videoName}</h6>
                    <button onclick="closeLSPVideoPopup()" class="close-btn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <video id="${videoId}" class="lsp-video" controls preload="metadata">
                    您的浏览器不支持视频播放
                </video>
            </div>
        `;

        // 添加到页面
        const popup = document.createElement('div');
        popup.className = 'lsp-video-popup-overlay';
        popup.innerHTML = videoHTML;
        document.body.appendChild(popup);

        // 初始化播放器
        setTimeout(() => {
            initLSPVideoPlayer(videoId, m3u8Url);
        }, 100);

    } catch (error) {
        console.error('播放LSP视频失败:', error);
        if (window.ToolsManager) {
            window.ToolsManager.showToast('播放失败，请重试', 'error');
        }
    }
}

// 关闭LSP视频弹窗
function closeLSPVideoPopup() {
    const popup = document.querySelector('.lsp-video-popup-overlay');
    if (popup) {
        // 清理HLS实例
        if (currentHLS) {
            currentHLS.destroy();
            currentHLS = null;
        }
        popup.remove();
    }
}

// 在新窗口打开视频
function openVideoInNewTab(m3u8Url) {
    try {
        const videoPage = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>LSP自律视频</title>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1">
                <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
                <style>
                    body { margin: 0; padding: 20px; background: #000; }
                    video { width: 100%; max-width: 800px; height: auto; }
                    .container { text-align: center; }
                    h1 { color: white; font-family: Arial, sans-serif; }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>LSP每日自律视频</h1>
                    <video id="video" controls autoplay>
                        您的浏览器不支持视频播放
                    </video>
                </div>
                <script>
                    const video = document.getElementById('video');
                    const m3u8Url = '${m3u8Url}';

                    if (Hls.isSupported()) {
                        const hls = new Hls();
                        hls.loadSource(m3u8Url);
                        hls.attachMedia(video);
                    } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
                        video.src = m3u8Url;
                    }
                </script>
            </body>
            </html>
        `;

        const blob = new Blob([videoPage], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        window.open(url, '_blank');

        // 清理URL对象
        setTimeout(() => URL.revokeObjectURL(url), 1000);

    } catch (error) {
        console.error('打开新窗口失败:', error);
        if (window.ToolsManager) {
            window.ToolsManager.showToast('打开失败，请重试', 'error');
        }
    }
}

// 初始化工具管理器
document.addEventListener('DOMContentLoaded', function() {
    window.ToolsManager = new ToolsManager();
});
