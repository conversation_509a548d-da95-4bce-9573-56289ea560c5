// 用户认证系统
class AuthManager {
    constructor() {
        this.currentUser = null;
        this.isLoggedIn = false;
        this.init();
    }

    // 初始化认证系统
    init() {
        this.checkLoginStatus();
        this.bindEvents();
        this.startSessionCheck();
    }

    // 绑定事件
    bindEvents() {
        // 登录表单
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleLogin();
            });
        }

        // 注册表单
        const registerForm = document.getElementById('registerForm');
        if (registerForm) {
            registerForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleRegister();
            });
        }
    }

    // 检查登录状态
    checkLoginStatus() {
        const token = localStorage.getItem(CONFIG.STORAGE_KEYS.USER_TOKEN);
        const userInfo = localStorage.getItem(CONFIG.STORAGE_KEYS.USER_INFO);
        const loginTime = localStorage.getItem(CONFIG.STORAGE_KEYS.LOGIN_TIME);

        if (token && userInfo && loginTime) {
            const now = Date.now();
            const loginTimestamp = parseInt(loginTime);
            
            // 检查会话是否过期
            if (now - loginTimestamp < CONFIG.APP.SESSION_TIMEOUT) {
                this.currentUser = JSON.parse(userInfo);
                this.isLoggedIn = true;
                this.showMainContent();
                return true;
            } else {
                this.logout();
            }
        }
        
        this.showLoginModal();
        return false;
    }

    // 处理登录
    async handleLogin() {
        const username = document.getElementById('loginUsername').value.trim();
        const password = document.getElementById('loginPassword').value;

        if (!username || !password) {
            UI.showToast('请输入用户名和密码', 'error');
            return;
        }

        this.showLoading();

        try {
            const response = await fetch(CONFIG.API.BASE_URL + CONFIG.API.ENDPOINTS.LOCAL_LOGIN, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ username, password })
            });

            const result = await response.json();

            if (result.code === 200) {
                // 登录成功
                this.currentUser = result.user;
                this.isLoggedIn = true;

                // 保存到本地存储
                localStorage.setItem(CONFIG.STORAGE_KEYS.USER_TOKEN, result.user.token);
                localStorage.setItem(CONFIG.STORAGE_KEYS.USER_INFO, JSON.stringify(result.user));
                localStorage.setItem(CONFIG.STORAGE_KEYS.LOGIN_TIME, Date.now().toString());

                this.hideLoading();
                this.showToast(CONFIG.SUCCESS_MESSAGES.LOGIN_SUCCESS, 'success');

                // 登录成功后刷新页面
                setTimeout(() => {
                    window.location.reload();
                }, 1000); // 1秒后刷新，让用户看到成功提示
            } else {
                this.hideLoading();
                this.showToast(result.message || '登录失败', 'error');
            }
        } catch (error) {
            this.hideLoading();
            this.showToast(CONFIG.ERROR_MESSAGES.NETWORK_ERROR, 'error');
            console.error('Login error:', error);
        }
    }

    // 处理注册
    async handleRegister() {
        const username = document.getElementById('registerUsername').value.trim();
        const password = document.getElementById('registerPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;

        // 验证输入
        if (!username || !password || !confirmPassword) {
            this.showToast('请填写所有字段', 'error');
            return;
        }

        if (password !== confirmPassword) {
            this.showToast('两次输入的密码不一致', 'error');
            return;
        }

        if (password.length < 6) {
            this.showToast('密码长度至少6位', 'error');
            return;
        }

        this.showLoading();

        try {
            // 生成唯一Token
            const token = Utils.generateToken();

            // 本地注册
            const response = await fetch(CONFIG.API.BASE_URL + CONFIG.API.ENDPOINTS.LOCAL_REGISTER, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ username, password, token })
            });

            const result = await response.json();

            if (result.code === 200) {
                // 向上游注册Token
                await this.registerUpstream(token);

                this.hideLoading();
                this.showToast(CONFIG.SUCCESS_MESSAGES.REGISTER_SUCCESS, 'success');
                this.closeModal('registerModal');

                // 自动登录
                document.getElementById('loginUsername').value = username;
                document.getElementById('loginPassword').value = password;
                this.handleLogin();
            } else {
                this.hideLoading();
                this.showToast(result.message || '注册失败', 'error');
            }
        } catch (error) {
            this.hideLoading();
            this.showToast(CONFIG.ERROR_MESSAGES.NETWORK_ERROR, 'error');
            console.error('Register error:', error);
        }
    }

    // 向上游注册Token
    async registerUpstream(token) {
        try {
            const response = await fetch(`${CONFIG.API.UPSTREAM_BASE_URL}${CONFIG.API.ENDPOINTS.UPSTREAM_REGISTER}?token=${token}`);
            const result = await response.json();
            
            if (result.code !== 200) {
                console.warn('Upstream registration warning:', result.message);
            }
        } catch (error) {
            console.error('Upstream registration error:', error);
        }
    }

    // 获取用户详细信息
    async fetchUserProfile() {
        if (!this.currentUser || !this.currentUser.token) return;

        try {
            // 使用本地API代理调用demo.php
            const response = await fetch(`${CONFIG.API.BASE_URL}/api/auth/user_status.php`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    token: this.currentUser.token
                })
            });

            const result = await response.json();

            if (result.code === 200 && result.user) {
                // 更新用户信息
                this.currentUser = { ...this.currentUser, ...result.user };
                localStorage.setItem(CONFIG.STORAGE_KEYS.USER_INFO, JSON.stringify(this.currentUser));

                // 更新VIP状态显示
                this.updateVipStatus();
            }
        } catch (error) {
            console.error('Fetch profile error:', error);
        }
    }

    // 退出登录
    logout() {
        this.currentUser = null;
        this.isLoggedIn = false;

        // 清除本地存储
        localStorage.removeItem(CONFIG.STORAGE_KEYS.USER_TOKEN);
        localStorage.removeItem(CONFIG.STORAGE_KEYS.USER_INFO);
        localStorage.removeItem(CONFIG.STORAGE_KEYS.LOGIN_TIME);

        this.showToast(CONFIG.SUCCESS_MESSAGES.LOGOUT_SUCCESS, 'info');
        this.showLoginModal();
    }

    // 显示登录模态框
    showLoginModal() {
        document.body.classList.add('auth-required');
        if (window.UI) {
            window.UI.openModal('loginModal');
        } else {
            // 降级方案：直接显示模态框
            const modal = document.getElementById('loginModal');
            if (modal) {
                modal.classList.add('show');
                modal.style.display = 'flex';
            }
        }
    }

    // 显示主要内容
    showMainContent() {
        document.body.classList.remove('auth-required');

        // 预加载用户状态（更新VIP状态显示）
        this.fetchUserProfile();

        // 初始化工具展示
        if (window.ToolsManager) {
            window.ToolsManager.init();
        }

        // 启动定期状态刷新（每5分钟检查一次）
        this.startStatusRefresh();
    }

    // 检查VIP状态（同步方法，基于本地缓存）
    isVIP() {
        if (!this.currentUser) return false;

        const vipcode = parseInt(this.currentUser.vipcode || 0);
        if (vipcode === 0) return false;

        // 检查VIP是否过期
        if (this.currentUser.viptime) {
            const vipTime = new Date(this.currentUser.viptime);
            const now = new Date();
            return vipTime > now;
        }

        return false;
    }

    // 异步检查VIP状态（从服务器获取最新状态）
    async checkVIPStatus() {
        if (!this.currentUser || !this.currentUser.token) return false;

        try {
            // 使用本地API代理调用demo.php
            const response = await fetch(`${CONFIG.API.BASE_URL}/api/auth/user_status.php`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    token: this.currentUser.token
                })
            });

            const result = await response.json();

            if (result.code === 200 && result.user) {
                // 更新本地用户信息
                this.currentUser = { ...this.currentUser, ...result.user };
                localStorage.setItem(CONFIG.STORAGE_KEYS.USER_INFO, JSON.stringify(this.currentUser));

                // 更新VIP状态显示
                this.updateVipStatus();

                // 检查VIP状态
                const vipcode = parseInt(result.user.vipcode || 0);
                if (vipcode === 0) return false;

                if (result.user.viptime) {
                    const vipTime = new Date(result.user.viptime);
                    const now = new Date();
                    return vipTime > now;
                }
            }

            return false;
        } catch (error) {
            console.error('Check VIP status error:', error);
            // 网络错误时回退到本地判断
            return this.isVIP();
        }
    }

    // 检查工具权限（保留用于其他地方可能的需要）
    hasToolPermission(tool) {
        if (!tool.vip) return true;
        return this.isVIP();
    }

    // 注意：工具权限检查现在主要由上游API处理
    // 前端不再在点击时检查VIP权限，避免重复验证

    // 开始会话检查
    startSessionCheck() {
        setInterval(() => {
            if (this.isLoggedIn) {
                const loginTime = localStorage.getItem(CONFIG.STORAGE_KEYS.LOGIN_TIME);
                if (loginTime) {
                    const now = Date.now();
                    const loginTimestamp = parseInt(loginTime);
                    const timeLeft = CONFIG.APP.SESSION_TIMEOUT - (now - loginTimestamp);
                    
                    // 5分钟前警告
                    if (timeLeft <= CONFIG.APP.AUTO_LOGOUT_WARNING && timeLeft > 0) {
                        this.showToast('会话即将过期，请保存您的工作', 'warning');
                    }

                    // 会话过期
                    if (timeLeft <= 0) {
                        this.showToast('会话已过期，请重新登录', 'error');
                        this.logout();
                    }
                }
            }
        }, 60000); // 每分钟检查一次
    }

    // 开始状态刷新（定期更新用户VIP状态）
    startStatusRefresh() {
        // 避免重复启动
        if (this.statusRefreshInterval) {
            clearInterval(this.statusRefreshInterval);
        }

        // 每5分钟更新一次用户状态
        this.statusRefreshInterval = setInterval(() => {
            if (this.isLoggedIn && this.currentUser) {
                this.fetchUserProfile();
            }
        }, 300000); // 5分钟 = 300000毫秒
    }

    // 获取当前用户信息
    getCurrentUser() {
        return this.currentUser;
    }

    // 获取用户Token
    getUserToken() {
        return this.currentUser ? this.currentUser.token : null;
    }

    // 更新用户信息
    updateUserInfo(newInfo) {
        if (this.currentUser) {
            this.currentUser = { ...this.currentUser, ...newInfo };

            // 更新本地存储
            localStorage.setItem(CONFIG.STORAGE_KEYS.USER_INFO, JSON.stringify(this.currentUser));

            // 更新VIP状态显示
            this.updateVipStatus();

            console.log('用户信息已更新:', this.currentUser);
        }
    }

    // 更新VIP状态显示
    updateVipStatus() {
        const profileBtn = document.querySelector('.profile-btn');
        if (profileBtn && this.currentUser) {
            if (this.currentUser.vipcode == 1 && this.currentUser.viptime) {
                const vipTime = new Date(this.currentUser.viptime);
                const now = new Date();

                if (vipTime > now) {
                    profileBtn.classList.add('vip');
                } else {
                    profileBtn.classList.remove('vip');
                }
            } else {
                profileBtn.classList.remove('vip');
            }
        }
    }

    // UI辅助方法
    showLoading(message = '加载中...') {
        if (window.UI) {
            window.UI.showLoading(message);
        }
    }

    hideLoading() {
        if (window.UI) {
            window.UI.hideLoading();
        }
    }

    showToast(message, type = 'info') {
        if (window.UI) {
            window.UI.showToast(message, type);
        } else {
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    }

    closeModal(modalId) {
        if (window.UI) {
            window.UI.closeModal(modalId);
        } else {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('show');
                modal.style.display = 'none';
            }
        }
    }
}

// 全局函数
function showLogin() {
    if (window.UI) {
        window.UI.closeModal('registerModal');
        window.UI.openModal('loginModal');
    } else {
        document.getElementById('registerModal').classList.remove('show');
        document.getElementById('loginModal').classList.add('show');
    }
}

function showRegister() {
    if (window.UI) {
        window.UI.closeModal('loginModal');
        window.UI.openModal('registerModal');
    } else {
        document.getElementById('loginModal').classList.remove('show');
        document.getElementById('registerModal').classList.add('show');
    }
}

function logout() {
    if (window.AuthManager) {
        window.AuthManager.logout();
    }
}

// 初始化认证管理器
document.addEventListener('DOMContentLoaded', function() {
    window.AuthManager = new AuthManager();
});
