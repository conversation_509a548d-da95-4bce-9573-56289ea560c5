<?php
/**
 * 卡泡聆听API - 简化版本
 * 获取音频文件
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 获取token参数
$token = $_GET['token'] ?? '';

// 基本参数验证
if (empty($token)) {
    echo json_encode([
        'code' => 400,
        'message' => '缺少token参数'
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 构建上游API请求URL
$upstreamUrl = 'https://api.qnm6.top/api/kp?token=' . urlencode($token);

// 最简单的方式：直接使用file_get_contents
$response = @file_get_contents($upstreamUrl);

if ($response === false) {
    echo json_encode([
        'code' => 500,
        'message' => '卡泡聆听服务暂时不可用，请稍后重试',
        'debug_url' => $upstreamUrl
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 直接输出响应，不做任何处理
echo $response;

?>
