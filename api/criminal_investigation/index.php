<?php
/**
 * 刑事侦查调档API
 * 生成刑事侦查相关文档
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // 获取请求参数
    $input = json_decode(file_get_contents('php://input'), true);
    
    // 支持GET和POST请求
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $token = $_GET['token'] ?? '';
        $xm = $_GET['xm'] ?? '';
        $hm = $_GET['hm'] ?? '';
        $ajxz = $_GET['ajxz'] ?? '';
        $ajlb = $_GET['ajlb'] ?? '';
        $danwei = $_GET['danwei'] ?? '';
        $whcd = $_GET['whcd'] ?? '';
    } else {
        $token = $input['token'] ?? '';
        $xm = $input['xm'] ?? '';
        $hm = $input['hm'] ?? '';
        $ajxz = $input['ajxz'] ?? '';
        $ajlb = $input['ajlb'] ?? '';
        $danwei = $input['danwei'] ?? '';
        $whcd = $input['whcd'] ?? '';
    }
    
    // 基本参数验证
    if (empty($token)) {
        echo json_encode([
            'code' => 400,
            'message' => '缺少token参数'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    if (empty($xm)) {
        echo json_encode([
            'code' => 400,
            'message' => '请输入姓名'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    if (empty($hm)) {
        echo json_encode([
            'code' => 400,
            'message' => '请输入身份证号码'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    if (empty($ajxz)) {
        echo json_encode([
            'code' => 400,
            'message' => '请选择案件性质'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    if (empty($ajlb)) {
        echo json_encode([
            'code' => 400,
            'message' => '请选择案件类别'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    if (empty($danwei)) {
        echo json_encode([
            'code' => 400,
            'message' => '请输入工作单位'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    if (empty($whcd)) {
        echo json_encode([
            'code' => 400,
            'message' => '请输入文化程度'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 验证姓名格式（2-4个中文字符）
    if (!preg_match('/^[\x{4e00}-\x{9fa5}]{2,4}$/u', $xm)) {
        echo json_encode([
            'code' => 400,
            'message' => '姓名格式不正确，请输入2-4个中文字符'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 验证身份证号格式
    if (!preg_match('/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/', $hm)) {
        echo json_encode([
            'code' => 400,
            'message' => '身份证号格式不正确'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 验证案件性质参数
    if (!in_array($ajxz, ['1', '2', '3', '4'])) {
        echo json_encode([
            'code' => 400,
            'message' => '案件性质参数错误'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 验证案件类别参数
    if (!in_array($ajlb, ['1', '2'])) {
        echo json_encode([
            'code' => 400,
            'message' => '案件类别参数错误'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 构建上游API请求URL
    $upstreamUrl = 'https://api.qnm6.top/api/xszc';
    $params = [
        'token' => $token,
        'xm' => $xm,
        'hm' => $hm,
        'ajxz' => $ajxz,
        'ajlb' => $ajlb,
        'danwei' => $danwei,
        'whcd' => $whcd
    ];
    
    $queryString = http_build_query($params);
    $fullUrl = $upstreamUrl . '?' . $queryString;
    
    // 使用简单的file_get_contents进行HTTP请求
    $response = @file_get_contents($fullUrl);
    
    if ($response === false) {
        echo json_encode([
            'code' => 500,
            'message' => '刑事侦查调档服务暂时不可用，请稍后重试'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 解析响应
    $result = json_decode($response, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        echo json_encode([
            'code' => 500,
            'message' => '刑事侦查调档结果解析失败'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 直接返回上游API的响应
    echo json_encode($result, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    
} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'message' => '系统错误：' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
