<?php
/**
 * 社交平台反查API
 * 支持抖音、快手、微信账号反查姓名身份证
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // 获取请求参数
    $input = json_decode(file_get_contents('php://input'), true);
    
    // 支持GET和POST请求
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $token = $_GET['token'] ?? '';
        $platform = $_GET['platform'] ?? '';
        $account = $_GET['account'] ?? '';
        $name = $_GET['name'] ?? '';
    } else {
        $token = $input['token'] ?? '';
        $platform = $input['platform'] ?? '';
        $account = $input['account'] ?? '';
        $name = $input['name'] ?? '';
    }
    
    // 基本参数验证
    if (empty($token)) {
        echo json_encode([
            'code' => 400,
            'message' => '缺少token参数'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    if (empty($platform)) {
        echo json_encode([
            'code' => 400,
            'message' => '请选择平台类型'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    if (empty($account)) {
        echo json_encode([
            'code' => 400,
            'message' => '请输入账号'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    if (empty($name)) {
        echo json_encode([
            'code' => 400,
            'message' => '请输入姓名'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 验证平台类型
    $validPlatforms = ['douyin', 'kuaishou', 'wechat'];
    if (!in_array($platform, $validPlatforms)) {
        echo json_encode([
            'code' => 400,
            'message' => '不支持的平台类型'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 构建上游API请求URL
    $upstreamUrl = 'https://api.qnm6.top/api/dujia/index.php';
    $params = [
        'token' => $token,
        'action' => 'platform_id',
        'name' => $name
    ];
    
    // 根据平台类型设置对应的参数
    switch ($platform) {
        case 'douyin':
            $params['douyin'] = $account;
            break;
        case 'kuaishou':
            $params['kuaishou'] = $account;
            break;
        case 'wechat':
            $params['wechat'] = $account;
            break;
    }
    
    $queryString = http_build_query($params);
    $fullUrl = $upstreamUrl . '?' . $queryString;
    
    // 使用cURL进行更可靠的HTTP请求
    $response = makeHttpRequest($fullUrl);
    
    if ($response === false) {
        echo json_encode([
            'code' => 500,
            'message' => '查询服务暂时不可用，请稍后重试'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 解析响应
    $result = json_decode($response, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        echo json_encode([
            'code' => 500,
            'message' => '查询结果解析失败'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 直接返回上游API的响应
    echo json_encode($result, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    
} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'message' => '系统错误：' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 使用cURL进行HTTP请求，支持重试机制
 */
function makeHttpRequest($url, $maxRetries = 3) {
    $retryCount = 0;

    while ($retryCount < $maxRetries) {
        $ch = curl_init();

        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 15,
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => 3,
            CURLOPT_USERAGENT => 'iDatas-SocialReverse/1.0',
            CURLOPT_HTTPHEADER => [
                'Accept: application/json',
                'Cache-Control: no-cache'
            ],
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);

        curl_close($ch);

        // 请求成功
        if ($response !== false && $httpCode == 200) {
            return $response;
        }

        $retryCount++;

        // 如果不是最后一次重试，等待一段时间再重试
        if ($retryCount < $maxRetries) {
            usleep(500000); // 等待0.5秒
        }
    }

    return false;
}
?>
