<?php
/**
 * 用户状态检查API
 * 调用上游demo.php获取用户最新状态
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // 获取请求参数
    $input = json_decode(file_get_contents('php://input'), true);
    
    // 支持GET和POST请求
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $token = $_GET['token'] ?? '';
    } else {
        $token = $input['token'] ?? '';
    }
    
    // 基本参数验证
    if (empty($token)) {
        echo json_encode([
            'code' => 400,
            'message' => '缺少token参数'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 构建上游API请求URL
    $upstreamUrl = 'https://api.qnm6.top/admapi/demo.php';
    $params = [
        'token' => $token
    ];
    
    $queryString = http_build_query($params);
    $fullUrl = $upstreamUrl . '?' . $queryString;
    
    // 发起HTTP请求到上游API
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'timeout' => 30,
            'header' => [
                'User-Agent: iDatas-Status/1.0',
                'Accept: application/json'
            ]
        ]
    ]);
    
    $response = file_get_contents($fullUrl, false, $context);
    
    if ($response === false) {
        echo json_encode([
            'code' => 500,
            'message' => '用户状态服务暂时不可用，请稍后重试'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 解析响应
    $result = json_decode($response, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        echo json_encode([
            'code' => 500,
            'message' => '用户状态服务响应解析失败'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 直接返回上游API的响应
    echo json_encode($result, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    
} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'message' => '系统错误：' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
